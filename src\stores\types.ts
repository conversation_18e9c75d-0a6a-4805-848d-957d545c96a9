
export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  isPinned: boolean;
  timestamp: Date;
}

export interface ChecklistItem {
  id: string;
  task: string;
  isComplete: boolean;
}

export interface Conversation {
  id: string;
  name: string;
  createdAt: Date;
  parentId?: string; // For branched conversations
  isContextShielded: boolean; // Controls visibility to compass & plan agents
  messages: {
    core: Message[];
    plan: Message[];
  };
}

export interface ModelConfig {
  model: string;
  temperature: number;
  maxOutputTokens: number;
  thinkingBudget?: number;
}

export interface AppState {
  conversations: Conversation[];
  activeConversationId: string;
  generalChatMessages: Message[];
  compassSummary: string;
  compassChecklist: ChecklistItem[];
  isLoading: {
    core: boolean;
    plan: boolean;
    compass: boolean;
    general: boolean;
  };
  apiKeys: {
    openai: string;
    anthropic: string;
    google: string;
    openrouter: string;
  };
  modelSettings: {
    core: ModelConfig;
    plan: ModelConfig;
    compass: ModelConfig;
    general: ModelConfig;
  };
  isSettingsModalOpen: boolean;
  isBranchModalOpen: boolean;
  branchTitle: string;
  currentWorkspace: {
    name: string;
    description: string;
  } | null;
  draftPrompt: string; // For Plan AI to prepare prompts for Core
}
