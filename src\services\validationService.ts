import { AppState, ModelConfig } from '../stores/types';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface ApiKeyValidationResult extends ValidationResult {
  service: keyof AppState['apiKeys'];
  key: string;
  source: 'environment' | 'localStorage' | 'user-input';
}

export interface ModelValidationResult extends ValidationResult {
  model: string;
  panel: keyof AppState['modelSettings'];
  requiredApiKey?: keyof AppState['apiKeys'];
}

export class ValidationService {
  private static instance: ValidationService;
  
  private constructor() {}
  
  public static getInstance(): ValidationService {
    if (!ValidationService.instance) {
      ValidationService.instance = new ValidationService();
    }
    return ValidationService.instance;
  }

  /**
   * Validate API key format and structure
   */
  public validateApiKey(service: keyof AppState['apiKeys'], key: string, source: 'environment' | 'localStorage' | 'user-input' = 'user-input'): ApiKeyValidationResult {
    const result: ApiKeyValidationResult = {
      isValid: false,
      errors: [],
      warnings: [],
      service,
      key,
      source,
    };

    if (!key || key.trim().length === 0) {
      result.errors.push(`${this.getServiceDisplayName(service)} API key is required`);
      return result;
    }

    const trimmedKey = key.trim();

    switch (service) {
      case 'openai':
        if (!trimmedKey.startsWith('sk-')) {
          result.errors.push('OpenAI API key must start with "sk-"');
        } else if (trimmedKey.length < 20) {
          result.errors.push('OpenAI API key appears to be too short');
        } else if (trimmedKey.length > 200) {
          result.errors.push('OpenAI API key appears to be too long');
        } else {
          result.isValid = true;
        }
        break;

      case 'anthropic':
        if (!trimmedKey.startsWith('sk-ant-')) {
          result.errors.push('Anthropic API key must start with "sk-ant-"');
        } else if (trimmedKey.length < 20) {
          result.errors.push('Anthropic API key appears to be too short');
        } else if (trimmedKey.length > 200) {
          result.errors.push('Anthropic API key appears to be too long');
        } else {
          result.isValid = true;
        }
        break;

      case 'google':
        if (!trimmedKey.startsWith('AIza')) {
          result.errors.push('Google API key must start with "AIza"');
        } else if (trimmedKey.length < 20) {
          result.errors.push('Google API key appears to be too short');
        } else if (trimmedKey.length > 200) {
          result.errors.push('Google API key appears to be too long');
        } else {
          result.isValid = true;
        }
        break;

      case 'openrouter':
        if (!trimmedKey.startsWith('sk-or-')) {
          result.errors.push('OpenRouter API key must start with "sk-or-"');
        } else if (trimmedKey.length < 20) {
          result.errors.push('OpenRouter API key appears to be too short');
        } else if (trimmedKey.length > 200) {
          result.errors.push('OpenRouter API key appears to be too long');
        } else {
          result.isValid = true;
        }
        break;

      default:
        result.errors.push(`Unknown API service: ${service}`);
    }

    // Add warnings based on source
    if (result.isValid && source === 'environment') {
      result.warnings.push(`Using ${this.getServiceDisplayName(service)} API key from environment variable`);
    }

    return result;
  }

  /**
   * Validate all API keys
   */
  public validateAllApiKeys(apiKeys: AppState['apiKeys'], sources?: Record<keyof AppState['apiKeys'], 'environment' | 'localStorage' | 'user-input'>): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    const services: (keyof AppState['apiKeys'])[] = ['openai', 'anthropic', 'google', 'openrouter'];
    
    services.forEach(service => {
      const key = apiKeys[service];
      const source = sources?.[service] || 'user-input';
      
      if (key && key.trim().length > 0) {
        const validation = this.validateApiKey(service, key, source);
        result.errors.push(...validation.errors);
        result.warnings.push(...validation.warnings);
        
        if (!validation.isValid) {
          result.isValid = false;
        }
      }
    });

    // Check if at least one API key is provided
    const hasAnyKey = services.some(service => apiKeys[service] && apiKeys[service].trim().length > 0);
    if (!hasAnyKey) {
      result.errors.push('At least one API key must be provided to use the application');
      result.isValid = false;
    }

    return result;
  }

  /**
   * Validate model configuration
   */
  public validateModelConfig(panel: keyof AppState['modelSettings'], config: ModelConfig, apiKeys: AppState['apiKeys']): ModelValidationResult {
    const result: ModelValidationResult = {
      isValid: false,
      errors: [],
      warnings: [],
      model: config.model,
      panel,
    };

    // Validate model name
    if (!config.model || config.model.trim().length === 0) {
      result.errors.push(`Model name is required for ${panel} panel`);
      return result;
    }

    // Validate temperature
    if (typeof config.temperature !== 'number' || config.temperature < 0 || config.temperature > 2) {
      result.errors.push(`Temperature must be a number between 0 and 2 for ${panel} panel`);
    }

    // Validate max output tokens
    if (typeof config.maxOutputTokens !== 'number' || config.maxOutputTokens < 1 || config.maxOutputTokens > 100000) {
      result.errors.push(`Max output tokens must be a number between 1 and 100,000 for ${panel} panel`);
    }

    // Validate thinking budget for Gemini models
    if (config.thinkingBudget !== undefined) {
      if (typeof config.thinkingBudget !== 'number' || config.thinkingBudget < 1 || config.thinkingBudget > 50000) {
        result.errors.push(`Thinking budget must be a number between 1 and 50,000 for ${panel} panel`);
      }
    }

    // Determine required API key based on model
    const requiredApiKey = this.getRequiredApiKeyForModel(config.model);
    result.requiredApiKey = requiredApiKey;

    if (requiredApiKey) {
      const apiKey = apiKeys[requiredApiKey];
      if (!apiKey || apiKey.trim().length === 0) {
        result.errors.push(`${this.getServiceDisplayName(requiredApiKey)} API key is required for model "${config.model}"`);
      } else {
        // Validate the API key format
        const keyValidation = this.validateApiKey(requiredApiKey, apiKey);
        if (!keyValidation.isValid) {
          result.errors.push(`Invalid ${this.getServiceDisplayName(requiredApiKey)} API key for model "${config.model}": ${keyValidation.errors.join(', ')}`);
        }
      }
    } else {
      result.warnings.push(`Could not determine required API key for model "${config.model}". Please ensure you have the correct API key configured.`);
    }

    result.isValid = result.errors.length === 0;
    return result;
  }

  /**
   * Validate all model settings
   */
  public validateAllModelSettings(modelSettings: AppState['modelSettings'], apiKeys: AppState['apiKeys']): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    const panels: (keyof AppState['modelSettings'])[] = ['core', 'plan', 'compass', 'general'];
    
    panels.forEach(panel => {
      const config = modelSettings[panel];
      const validation = this.validateModelConfig(panel, config, apiKeys);
      
      result.errors.push(...validation.errors);
      result.warnings.push(...validation.warnings);
      
      if (!validation.isValid) {
        result.isValid = false;
      }
    });

    return result;
  }

  /**
   * Get required API key for a model
   */
  private getRequiredApiKeyForModel(model: string): keyof AppState['apiKeys'] | null {
    const lowerModel = model.toLowerCase();
    
    if (lowerModel.startsWith('gpt') || lowerModel.includes('openai')) {
      return 'openai';
    }
    
    if (lowerModel.startsWith('claude') || lowerModel.includes('anthropic')) {
      return 'anthropic';
    }
    
    if (lowerModel.startsWith('gemini') || lowerModel.includes('google')) {
      return 'google';
    }
    
    if (lowerModel.includes('/') || lowerModel.startsWith('deepseek') || lowerModel.includes('openrouter')) {
      return 'openrouter';
    }
    
    return null;
  }

  /**
   * Get display name for API service
   */
  public getServiceDisplayName(service: keyof AppState['apiKeys']): string {
    switch (service) {
      case 'openai': return 'OpenAI';
      case 'anthropic': return 'Anthropic';
      case 'google': return 'Google';
      case 'openrouter': return 'OpenRouter';
      default: return service;
    }
  }

  /**
   * Validate storage availability and capacity
   */
  public validateStorageCapacity(): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    try {
      // Test localStorage availability
      const test = '__storage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
    } catch (error) {
      result.isValid = false;
      result.errors.push('Local storage is not available. Your settings and conversations will not be saved.');
      return result;
    }

    try {
      // Estimate storage usage
      let totalSize = 0;
      for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          totalSize += localStorage[key].length + key.length;
        }
      }

      // Warn if storage is getting full (assuming 5MB limit for most browsers)
      const estimatedLimit = 5 * 1024 * 1024; // 5MB in bytes
      const usagePercentage = (totalSize / estimatedLimit) * 100;

      if (usagePercentage > 80) {
        result.warnings.push(`Local storage is ${Math.round(usagePercentage)}% full. Consider exporting your data to free up space.`);
      } else if (usagePercentage > 90) {
        result.errors.push(`Local storage is ${Math.round(usagePercentage)}% full. You may experience issues saving data.`);
        result.isValid = false;
      }

    } catch (error) {
      result.warnings.push('Could not determine storage usage. Monitor for storage-related issues.');
    }

    return result;
  }

  /**
   * Comprehensive configuration validation
   */
  public validateConfiguration(appState: AppState): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    // Validate API keys
    const apiKeyValidation = this.validateAllApiKeys(appState.apiKeys);
    result.errors.push(...apiKeyValidation.errors);
    result.warnings.push(...apiKeyValidation.warnings);
    if (!apiKeyValidation.isValid) result.isValid = false;

    // Validate model settings
    const modelValidation = this.validateAllModelSettings(appState.modelSettings, appState.apiKeys);
    result.errors.push(...modelValidation.errors);
    result.warnings.push(...modelValidation.warnings);
    if (!modelValidation.isValid) result.isValid = false;

    // Validate storage
    const storageValidation = this.validateStorageCapacity();
    result.errors.push(...storageValidation.errors);
    result.warnings.push(...storageValidation.warnings);
    if (!storageValidation.isValid) result.isValid = false;

    return result;
  }
}

export default ValidationService.getInstance();
