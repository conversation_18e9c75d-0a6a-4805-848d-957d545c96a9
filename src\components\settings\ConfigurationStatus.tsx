import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, AlertTriangle, XCircle, RefreshCw, Shield, Database, Key } from 'lucide-react';
import { AppState } from '@/stores/types';
import { ValidationResult } from '@/services/validationService';
import validationService from '@/services/validationService';
import configService from '@/services/configService';

interface ConfigurationStatusProps {
  apiKeys: AppState['apiKeys'];
  modelSettings: AppState['modelSettings'];
  validation: ValidationResult;
  onRefreshValidation: () => void;
}

const ConfigurationStatus: React.FC<ConfigurationStatusProps> = ({
  apiKeys,
  modelSettings,
  validation,
  onRefreshValidation,
}) => {
  const apiKeyConfig = configService.loadApiKeys();
  const storageInfo = configService.getStorageInfo();

  const getStatusIcon = (isValid: boolean, hasWarnings: boolean) => {
    if (isValid && !hasWarnings) return <CheckCircle className="w-5 h-5 text-green-500" />;
    if (isValid && hasWarnings) return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
    return <XCircle className="w-5 h-5 text-red-500" />;
  };

  const getStatusBadge = (isValid: boolean, hasWarnings: boolean) => {
    if (isValid && !hasWarnings) return <Badge className="bg-green-900 text-green-100">Healthy</Badge>;
    if (isValid && hasWarnings) return <Badge className="bg-yellow-900 text-yellow-100">Warning</Badge>;
    return <Badge variant="destructive">Error</Badge>;
  };

  const apiKeyValidation = validationService.validateAllApiKeys(apiKeys);
  const modelValidation = validationService.validateAllModelSettings(modelSettings, apiKeys);
  const storageValidation = validationService.validateStorageCapacity();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-blue-400">Configuration Status</h3>
        <Button
          onClick={onRefreshValidation}
          variant="outline"
          size="sm"
          className="border-gray-600 text-gray-300 hover:bg-gray-800"
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Overall Status */}
      <Card className="bg-gray-800 border-gray-700">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-white flex items-center gap-2">
              {getStatusIcon(validation.isValid, validation.warnings.length > 0)}
              Overall Status
            </CardTitle>
            {getStatusBadge(validation.isValid, validation.warnings.length > 0)}
          </div>
        </CardHeader>
        <CardContent>
          {validation.errors.length > 0 && (
            <div className="mb-3">
              <p className="text-sm font-medium text-red-400 mb-2">Errors:</p>
              <ul className="text-sm text-red-300 space-y-1">
                {validation.errors.map((error, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <XCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
                    {error}
                  </li>
                ))}
              </ul>
            </div>
          )}
          {validation.warnings.length > 0 && (
            <div>
              <p className="text-sm font-medium text-yellow-400 mb-2">Warnings:</p>
              <ul className="text-sm text-yellow-300 space-y-1">
                {validation.warnings.map((warning, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <AlertTriangle className="w-4 h-4 mt-0.5 flex-shrink-0" />
                    {warning}
                  </li>
                ))}
              </ul>
            </div>
          )}
          {validation.isValid && validation.errors.length === 0 && validation.warnings.length === 0 && (
            <p className="text-sm text-green-300">All configurations are valid and ready to use.</p>
          )}
        </CardContent>
      </Card>

      {/* API Keys Status */}
      <Card className="bg-gray-800 border-gray-700">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-white flex items-center gap-2">
              <Key className="w-5 h-5" />
              API Keys
            </CardTitle>
            {getStatusBadge(apiKeyValidation.isValid, apiKeyValidation.warnings.length > 0)}
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Object.entries(apiKeyConfig).map(([service, config]) => {
              const hasKey = config.value && config.value.trim().length > 0;
              const validation = hasKey ? validationService.validateApiKey(service as keyof AppState['apiKeys'], config.value, config.source.source) : null;
              
              return (
                <div key={service} className="flex items-center justify-between p-3 bg-gray-900 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      {hasKey && validation?.isValid ? (
                        <CheckCircle className="w-4 h-4 text-green-500" />
                      ) : hasKey && !validation?.isValid ? (
                        <XCircle className="w-4 h-4 text-red-500" />
                      ) : (
                        <div className="w-4 h-4 rounded-full bg-gray-600" />
                      )}
                      <span className="text-sm font-medium text-white capitalize">
                        {validationService.getServiceDisplayName(service as keyof AppState['apiKeys'])}
                      </span>
                    </div>
                    {hasKey && (
                      <Badge 
                        variant="outline" 
                        className={`text-xs ${
                          config.source.source === 'environment' 
                            ? 'border-blue-500 text-blue-300' 
                            : 'border-gray-500 text-gray-300'
                        }`}
                      >
                        <Shield className="w-3 h-3 mr-1" />
                        {config.source.source === 'environment' ? 'ENV' : 'Stored'}
                      </Badge>
                    )}
                  </div>
                  <div className="text-xs text-gray-400">
                    {hasKey ? (
                      validation?.isValid ? 'Valid' : 'Invalid'
                    ) : (
                      'Not configured'
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Model Settings Status */}
      <Card className="bg-gray-800 border-gray-700">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-white flex items-center gap-2">
              <Database className="w-5 h-5" />
              Model Settings
            </CardTitle>
            {getStatusBadge(modelValidation.isValid, modelValidation.warnings.length > 0)}
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Object.entries(modelSettings).map(([panel, config]) => {
              const validation = validationService.validateModelConfig(panel as keyof AppState['modelSettings'], config, apiKeys);
              
              return (
                <div key={panel} className="flex items-center justify-between p-3 bg-gray-900 rounded-lg">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(validation.isValid, validation.warnings.length > 0)}
                    <div>
                      <span className="text-sm font-medium text-white capitalize">{panel}</span>
                      <p className="text-xs text-gray-400">{config.model}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-xs text-gray-400">
                      {validation.isValid ? 'Valid' : 'Issues'}
                    </div>
                    {validation.requiredApiKey && (
                      <div className="text-xs text-gray-500">
                        Requires {validationService.getServiceDisplayName(validation.requiredApiKey)}
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Storage Status */}
      <Card className="bg-gray-800 border-gray-700">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-white flex items-center gap-2">
              <Database className="w-5 h-5" />
              Storage
            </CardTitle>
            {getStatusBadge(storageValidation.isValid, storageValidation.warnings.length > 0)}
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-300">Status:</span>
              <span className={storageInfo.available ? 'text-green-400' : 'text-red-400'}>
                {storageInfo.available ? 'Available' : 'Unavailable'}
              </span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-300">Data stored:</span>
              <span className="text-gray-400">{storageInfo.keys.length} items</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-300">Storage used:</span>
              <span className="text-gray-400">
                {(storageInfo.used / 1024).toFixed(1)} KB
              </span>
            </div>
            {storageValidation.warnings.length > 0 && (
              <div className="mt-3 p-2 bg-yellow-900/20 border border-yellow-700 rounded">
                <p className="text-xs text-yellow-300">{storageValidation.warnings[0]}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ConfigurationStatus;
