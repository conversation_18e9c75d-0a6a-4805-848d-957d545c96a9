
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import modern fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 215 25% 27%;

    --card: 0 0% 100%;
    --card-foreground: 215 25% 27%;

    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 27%;

    --primary: 215 25% 27%;
    --primary-foreground: 0 0% 98%;

    --secondary: 210 11% 96%;
    --secondary-foreground: 215 25% 27%;

    --muted: 210 11% 96%;
    --muted-foreground: 215 16% 47%;

    --accent: 210 11% 96%;
    --accent-foreground: 215 25% 27%;

    --destructive: 0 65% 51%;
    --destructive-foreground: 0 0% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 215 25% 27%;

    --radius: 8px;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 215 25% 27%;
    --sidebar-primary: 215 25% 27%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 210 11% 96%;
    --sidebar-accent-foreground: 215 25% 27%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 215 25% 27%;
  }

  .dark {
    --background: 215 28% 17%;
    --foreground: 213 31% 91%;

    --card: 215 28% 17%;
    --card-foreground: 213 31% 91%;

    --popover: 215 28% 17%;
    --popover-foreground: 213 31% 91%;

    --primary: 213 31% 91%;
    --primary-foreground: 215 28% 17%;

    --secondary: 215 25% 27%;
    --secondary-foreground: 213 31% 91%;

    --muted: 215 25% 27%;
    --muted-foreground: 217 10% 64%;

    --accent: 215 25% 27%;
    --accent-foreground: 213 31% 91%;

    --destructive: 0 65% 51%;
    --destructive-foreground: 213 31% 91%;

    --border: 215 25% 27%;
    --input: 215 25% 27%;
    --ring: 213 31% 91%;

    --sidebar-background: 215 28% 17%;
    --sidebar-foreground: 213 31% 91%;
    --sidebar-primary: 213 31% 91%;
    --sidebar-primary-foreground: 215 28% 17%;
    --sidebar-accent: 215 25% 27%;
    --sidebar-accent-foreground: 213 31% 91%;
    --sidebar-border: 215 25% 27%;
    --sidebar-ring: 213 31% 91%;
  }
}

@layer base {
  * {
    @apply border-border transition-all duration-200 ease-in-out;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    line-height: 1.6;
    letter-spacing: -0.01em;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-medium;
    letter-spacing: -0.025em;
    line-height: 1.4;
  }

  h1 {
    @apply text-3xl font-semibold;
  }

  h2 {
    @apply text-2xl font-semibold;
  }

  h3 {
    @apply text-xl font-medium;
  }
}

/* Code highlighting styles for react-markdown */
.prose pre {
  @apply bg-muted text-foreground p-6 rounded-xl overflow-x-auto border;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.prose code {
  @apply bg-muted text-foreground px-2 py-1 rounded-md text-sm border;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.prose pre code {
  @apply bg-transparent p-0 border-0;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-transparent;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/20 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground/30;
}

/* Panel resize handle styling */
[data-panel-resize-handle-enabled] {
  @apply bg-border/50 hover:bg-accent transition-colors duration-200;
}

/* Focus styles */
button:focus-visible,
input:focus-visible,
textarea:focus-visible {
  @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
}

/* Smooth animations for all interactive elements */
button, a, [role="button"] {
  @apply transition-all duration-200 ease-in-out;
}

button:hover, a:hover, [role="button"]:hover {
  @apply scale-[1.02] transition-transform duration-200 ease-in-out;
}

button:active, a:active, [role="button"]:active {
  @apply scale-[0.98] transition-transform duration-100 ease-in-out;
}
