import { useMemo } from 'react';
import { AppState } from '@/stores/types';
import validationService from '@/services/validationService';
import configService from '@/services/configService';

export interface ConfigurationStatus {
  isValid: boolean;
  hasApiKeys: boolean;
  hasValidApiKeys: boolean;
  missingApiKeys: string[];
  invalidApiKeys: string[];
  environmentApiKeys: string[];
  modelIssues: string[];
  storageIssues: string[];
  overallHealth: 'healthy' | 'warning' | 'error';
  criticalIssues: string[];
  warnings: string[];
}

export const useConfigurationStatus = (
  apiKeys: AppState['apiKeys'],
  modelSettings: AppState['modelSettings']
): ConfigurationStatus => {
  return useMemo(() => {
    const apiKeyConfig = configService.loadApiKeys();
    const validation = validationService.validateConfiguration({
      apiKeys,
      modelSettings,
      conversations: [],
      generalChatMessages: [],
      compassSummary: '',
      compassChecklist: [],
      isLoading: { core: false, plan: false, compass: false, general: false },
      isSettingsModalOpen: false,
      isBranchModalOpen: false,
      branchTitle: '',
      currentWorkspace: null,
      draftPrompt: '',
      activeConversationId: '',
    });

    // Check API keys
    const hasApiKeys = Object.values(apiKeys).some(key => key && key.trim().length > 0);
    const missingApiKeys: string[] = [];
    const invalidApiKeys: string[] = [];
    const environmentApiKeys: string[] = [];

    Object.entries(apiKeyConfig).forEach(([service, config]) => {
      const serviceName = validationService.getServiceDisplayName(service as keyof AppState['apiKeys']);
      
      if (config.source.source === 'environment') {
        environmentApiKeys.push(serviceName);
      }
      
      if (!config.value || config.value.trim().length === 0) {
        missingApiKeys.push(serviceName);
      } else {
        const keyValidation = validationService.validateApiKey(
          service as keyof AppState['apiKeys'], 
          config.value, 
          config.source.source
        );
        if (!keyValidation.isValid) {
          invalidApiKeys.push(serviceName);
        }
      }
    });

    const hasValidApiKeys = Object.entries(apiKeys).some(([service, key]) => {
      if (!key || key.trim().length === 0) return false;
      const validation = validationService.validateApiKey(service as keyof AppState['apiKeys'], key);
      return validation.isValid;
    });

    // Check model settings
    const modelIssues: string[] = [];
    Object.entries(modelSettings).forEach(([panel, config]) => {
      const modelValidation = validationService.validateModelConfig(
        panel as keyof AppState['modelSettings'], 
        config, 
        apiKeys
      );
      if (!modelValidation.isValid) {
        modelIssues.push(`${panel}: ${modelValidation.errors[0]}`);
      }
    });

    // Check storage
    const storageValidation = validationService.validateStorageCapacity();
    const storageIssues = storageValidation.errors;

    // Determine critical issues
    const criticalIssues: string[] = [];
    
    if (!hasApiKeys) {
      criticalIssues.push('No API keys configured');
    } else if (!hasValidApiKeys) {
      criticalIssues.push('No valid API keys found');
    }
    
    if (modelIssues.length > 0) {
      criticalIssues.push(`Model configuration issues (${modelIssues.length})`);
    }
    
    if (storageIssues.length > 0) {
      criticalIssues.push('Storage issues detected');
    }

    // Determine warnings
    const warnings: string[] = [];
    
    if (invalidApiKeys.length > 0) {
      warnings.push(`Invalid API keys: ${invalidApiKeys.join(', ')}`);
    }
    
    if (validation.warnings.length > 0) {
      warnings.push(...validation.warnings);
    }

    // Determine overall health
    let overallHealth: 'healthy' | 'warning' | 'error' = 'healthy';
    
    if (criticalIssues.length > 0) {
      overallHealth = 'error';
    } else if (warnings.length > 0 || invalidApiKeys.length > 0) {
      overallHealth = 'warning';
    }

    return {
      isValid: validation.isValid,
      hasApiKeys,
      hasValidApiKeys,
      missingApiKeys,
      invalidApiKeys,
      environmentApiKeys,
      modelIssues,
      storageIssues,
      overallHealth,
      criticalIssues,
      warnings,
    };
  }, [apiKeys, modelSettings]);
};

export default useConfigurationStatus;
