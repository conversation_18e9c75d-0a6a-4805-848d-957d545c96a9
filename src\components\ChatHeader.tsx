
import React from 'react';
import { GitBranch, Building, Zap, MessageSquare, Edit3, Loader2, Shield, ShieldOff, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';

// As Conversation type is not exported from the store, we define a local stub.
interface ConversationStub {
  id: string;
  name: string;
  parentId?: string | null;
  isContextShielded: boolean;
  createdAt: Date;
}

interface ChatHeaderProps {
  activeMode: 'core' | 'plan' | 'general';
  setActiveMode: (mode: 'core' | 'plan' | 'general') => void;
  activeConversation: ConversationStub | null;
  generalChatMessagesCount: number;
  coreMessagesCount: number;
  planMessagesCount: number;
  getCoreContextInfo: () => string;
  handleCreatePromptDraft: () => void;
  isGeneratingDraft: boolean;
  handleToggleContextShield: () => void;
  handleBranchConversation: () => void;
  clearGeneralChat: () => void;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({
  activeMode,
  setActiveMode,
  activeConversation,
  generalChatMessagesCount,
  coreMessagesCount,
  planMessagesCount,
  getCoreContextInfo,
  handleCreatePromptDraft,
  isGeneratingDraft,
  handleToggleContextShield,
  handleBranchConversation,
  clearGeneralChat,
}) => {

  const getModeDescription = () => {
    if (activeMode === 'core') return 'Systematic problem-solving and structured analysis';
    if (activeMode === 'plan') return 'Creative catalyst and idea refinement - with Core context';
    return 'Chat about anything, context-free.';
  };

  return (
    <div className="bg-card px-4 sm:px-6 py-4 sm:py-5 border-b border-border">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
        <div className="min-w-0 flex-1">
          <div className="flex items-center gap-2 mb-1">
            <h2 className="text-lg sm:text-xl font-semibold text-card-foreground truncate">
              {activeMode === 'general' ? 'General Chat' : activeConversation?.name}
            </h2>
            {activeConversation?.parentId && (
              <div className="flex items-center gap-1 text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                <GitBranch className="w-3 h-3" />
                Branch
              </div>
            )}
          </div>
          <p className="text-xs sm:text-sm text-muted-foreground">
            {activeMode === 'general' 
              ? `${generalChatMessagesCount} messages`
              : activeMode === 'plan' 
              ? `${planMessagesCount} plan messages • ${getCoreContextInfo()}`
              : `${coreMessagesCount} messages • Created ${activeConversation?.createdAt.toLocaleDateString()}`
            }
          </p>
        </div>
        <div className="flex items-center gap-1 bg-muted rounded-xl p-1 flex-shrink-0">
          <button
            onClick={() => setActiveMode('core')}
            className={`flex items-center gap-2 px-3 sm:px-4 py-2 sm:py-3 rounded-lg text-xs sm:text-sm font-medium transition-all duration-200 min-w-0 ${
              activeMode === 'core'
                ? 'bg-primary text-primary-foreground shadow-soft'
                : 'text-muted-foreground hover:text-foreground hover:bg-accent'
            }`}
          >
            <Building className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
            <span className="hidden xs:inline">Core</span>
            <span className="xs:hidden">Core</span>
          </button>
          <button
            onClick={() => setActiveMode('plan')}
            className={`flex items-center gap-2 px-3 sm:px-4 py-2 sm:py-3 rounded-lg text-xs sm:text-sm font-medium transition-all duration-200 min-w-0 ${
              activeMode === 'plan'
                ? 'bg-primary text-primary-foreground shadow-soft'
                : 'text-muted-foreground hover:text-foreground hover:bg-accent'
            }`}
          >
            <Zap className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
            <span className="hidden xs:inline">Plan</span>
            <span className="xs:hidden">Plan</span>
          </button>
          <button
            onClick={() => setActiveMode('general')}
            className={`flex items-center gap-2 px-3 sm:px-4 py-2 sm:py-3 rounded-lg text-xs sm:text-sm font-medium transition-all duration-200 min-w-0 ${
              activeMode === 'general'
                ? 'bg-primary text-primary-foreground shadow-soft'
                : 'text-muted-foreground hover:text-foreground hover:bg-accent'
            }`}
          >
            <MessageSquare className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
            <span className="hidden xs:inline">General</span>
            <span className="xs:hidden">Gen</span>
          </button>
        </div>
      </div>
      <div className="flex justify-between items-center">
        <p className="text-xs sm:text-sm text-muted-foreground">
          {getModeDescription()}
        </p>
        <div className="flex items-center gap-2">
          {activeMode === 'plan' && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCreatePromptDraft}
              className="flex items-center gap-1 text-xs"
              disabled={isGeneratingDraft}
            >
              {isGeneratingDraft ? (
                <Loader2 className="w-3 h-3 animate-spin" />
              ) : (
                <Edit3 className="w-3 h-3" />
              )}
              {isGeneratingDraft ? 'Generating...' : 'Draft for Core'}
            </Button>
          )}

          {activeMode !== 'general' && activeConversation && (
            <div className="flex items-center gap-2">
              <Label htmlFor="context-shield" className="text-xs text-muted-foreground flex items-center gap-1">
                {activeConversation.isContextShielded ? (
                  <Shield className="w-3 h-3" />
                ) : (
                  <ShieldOff className="w-3 h-3" />
                )}
                Context Shield
              </Label>
              <Switch
                id="context-shield"
                checked={activeConversation.isContextShielded}
                onCheckedChange={handleToggleContextShield}
              />
            </div>
          )}
          
          {activeMode === 'core' && activeConversation && coreMessagesCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBranchConversation}
              className="flex items-center gap-1 text-xs"
            >
              <GitBranch className="w-3 h-3" />
              Branch
            </Button>
          )}
          
          {activeMode === 'general' && generalChatMessagesCount > 0 && (
            <button
              onClick={clearGeneralChat}
              className="flex items-center gap-1.5 text-xs text-destructive hover:text-destructive/80 transition-colors"
              title="Clear General Chat"
            >
              <Trash2 className="w-3 h-3" />
              <span>Clear</span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatHeader;
