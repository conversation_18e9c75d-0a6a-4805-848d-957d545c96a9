
import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Send, Lightbulb } from 'lucide-react';
import useAppStore from '@/stores/useAppStore';

interface PromptDraftModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialPrompt?: string;
}

const PromptDraftModal: React.FC<PromptDraftModalProps> = ({ isOpen, onClose, initialPrompt = '' }) => {
  const { draftPrompt, setDraftPrompt, submitPromptToCore } = useAppStore();

  React.useEffect(() => {
    if (initialPrompt && isOpen) {
      setDraftPrompt(initialPrompt);
    }
  }, [initialPrompt, isOpen, setDraftPrompt]);

  const handleSubmit = () => {
    if (draftPrompt.trim()) {
      submitPromptToCore(draftPrompt.trim());
      onClose();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      handleSubmit();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Lightbulb className="w-5 h-5 text-primary" />
            Review AI-Generated Prompt
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="text-sm text-muted-foreground p-3 bg-accent/50 rounded-md border border-accent">
            <p>
              This draft was generated by Plan AI based on your recent discussion. 
              Review and edit the prompt below, then send it to Core AI for analysis.
            </p>
          </div>
          <div className="space-y-2">
            <Label htmlFor="prompt-draft">Prompt for Core AI</Label>
            <Textarea
              id="prompt-draft"
              placeholder="Review and edit the AI-generated prompt for Core AI..."
              value={draftPrompt}
              onChange={(e) => setDraftPrompt(e.target.value)}
              onKeyDown={handleKeyDown}
              rows={8}
              className="resize-none"
              autoFocus
            />
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={handleSubmit} disabled={!draftPrompt.trim()}>
              <Send className="w-4 h-4 mr-2" />
              Send to Core AI
            </Button>
          </div>
          <div className="text-xs text-muted-foreground">
            Tip: Press Ctrl+Enter (Cmd+Enter on Mac) to submit quickly
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PromptDraftModal;
