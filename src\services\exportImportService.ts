import { AppState } from '../stores/types';
import configService from './configService';

export interface ExportData {
  version: string;
  exportDate: string;
  apiKeys: AppState['apiKeys'];
  modelSettings: AppState['modelSettings'];
  conversations: AppState['conversations'];
  generalChatMessages: AppState['generalChatMessages'];
  compassSummary: string;
  compassChecklist: AppState['compassChecklist'];
  appSettings: {
    activeConversationId: string;
    currentWorkspace: AppState['currentWorkspace'];
  };
}

export interface ImportResult {
  success: boolean;
  message: string;
  warnings?: string[];
  importedData?: Partial<ExportData>;
}

export class ExportImportService {
  private static instance: ExportImportService;
  
  private constructor() {}
  
  public static getInstance(): ExportImportService {
    if (!ExportImportService.instance) {
      ExportImportService.instance = new ExportImportService();
    }
    return ExportImportService.instance;
  }

  /**
   * Export all application data to a JSON file
   */
  public async exportData(appState: AppState): Promise<void> {
    try {
      const exportData: ExportData = {
        version: '1.0',
        exportDate: new Date().toISOString(),
        apiKeys: appState.apiKeys,
        modelSettings: appState.modelSettings,
        conversations: appState.conversations,
        generalChatMessages: appState.generalChatMessages,
        compassSummary: appState.compassSummary,
        compassChecklist: appState.compassChecklist,
        appSettings: {
          activeConversationId: appState.activeConversationId,
          currentWorkspace: appState.currentWorkspace,
        },
      };

      const jsonString = JSON.stringify(exportData, null, 2);
      const blob = new Blob([jsonString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.download = `tandem-cognition-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      // Update last backup date in storage
      const currentSettings = configService.loadAppSettings() || {
        activeConversationId: appState.activeConversationId,
        currentWorkspace: appState.currentWorkspace,
      };
      
      configService.saveAppSettings({
        ...currentSettings,
        lastBackupDate: new Date(),
      });

    } catch (error) {
      console.error('Failed to export data:', error);
      throw new Error('Failed to export data. Please try again.');
    }
  }

  /**
   * Import data from a JSON file
   */
  public async importData(file: File): Promise<ImportResult> {
    try {
      const text = await this.readFileAsText(file);
      const data = JSON.parse(text) as ExportData;

      // Validate the imported data structure
      const validationResult = this.validateImportData(data);
      if (!validationResult.isValid) {
        return {
          success: false,
          message: `Invalid import file: ${validationResult.errors.join(', ')}`,
        };
      }

      const warnings: string[] = [];

      // Import API keys (but don't overwrite environment variables)
      if (data.apiKeys) {
        const currentApiKeys = configService.loadApiKeys();
        const keysToImport: AppState['apiKeys'] = { ...data.apiKeys };
        
        // Don't overwrite keys that come from environment variables
        Object.entries(currentApiKeys).forEach(([service, config]) => {
          if (config.source.source === 'environment') {
            (keysToImport as any)[service] = config.value;
            warnings.push(`${service} API key not imported (using environment variable)`);
          }
        });

        configService.saveApiKeys(keysToImport);
      }

      // Import model settings
      if (data.modelSettings) {
        configService.saveModelSettings(data.modelSettings);
      }

      // Import conversations
      if (data.conversations) {
        configService.saveConversations(data.conversations);
      }

      // Import general chat messages
      if (data.generalChatMessages) {
        configService.saveGeneralChat(data.generalChatMessages);
      }

      // Import compass data
      if (data.compassSummary || data.compassChecklist) {
        configService.saveCompassData(
          data.compassSummary || '',
          data.compassChecklist || []
        );
      }

      // Import app settings
      if (data.appSettings) {
        configService.saveAppSettings({
          activeConversationId: data.appSettings.activeConversationId,
          currentWorkspace: data.appSettings.currentWorkspace,
        });
      }

      return {
        success: true,
        message: 'Data imported successfully. Please refresh the page to see the changes.',
        warnings: warnings.length > 0 ? warnings : undefined,
        importedData: data,
      };

    } catch (error) {
      console.error('Failed to import data:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to import data. Please check the file format.',
      };
    }
  }

  /**
   * Read file as text
   */
  private readFileAsText(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          resolve(event.target.result as string);
        } else {
          reject(new Error('Failed to read file'));
        }
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    });
  }

  /**
   * Validate imported data structure
   */
  private validateImportData(data: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!data || typeof data !== 'object') {
      errors.push('Invalid file format');
      return { isValid: false, errors };
    }

    if (!data.version) {
      errors.push('Missing version information');
    }

    if (!data.exportDate) {
      errors.push('Missing export date');
    }

    // Validate API keys structure
    if (data.apiKeys && typeof data.apiKeys === 'object') {
      const requiredKeys = ['openai', 'anthropic', 'google', 'openrouter'];
      const hasValidStructure = requiredKeys.every(key => 
        key in data.apiKeys && typeof data.apiKeys[key] === 'string'
      );
      if (!hasValidStructure) {
        errors.push('Invalid API keys structure');
      }
    }

    // Validate model settings structure
    if (data.modelSettings && typeof data.modelSettings === 'object') {
      const requiredPanels = ['core', 'plan', 'compass', 'general'];
      const hasValidStructure = requiredPanels.every(panel => {
        const config = data.modelSettings[panel];
        return config && 
               typeof config.model === 'string' &&
               typeof config.temperature === 'number' &&
               typeof config.maxOutputTokens === 'number';
      });
      if (!hasValidStructure) {
        errors.push('Invalid model settings structure');
      }
    }

    // Validate conversations structure
    if (data.conversations && Array.isArray(data.conversations)) {
      const hasValidStructure = data.conversations.every((conv: any) => 
        conv && 
        typeof conv.id === 'string' &&
        typeof conv.name === 'string' &&
        conv.messages &&
        Array.isArray(conv.messages.core) &&
        Array.isArray(conv.messages.plan)
      );
      if (!hasValidStructure) {
        errors.push('Invalid conversations structure');
      }
    }

    // Validate general chat messages
    if (data.generalChatMessages && !Array.isArray(data.generalChatMessages)) {
      errors.push('Invalid general chat messages structure');
    }

    // Validate compass checklist
    if (data.compassChecklist && !Array.isArray(data.compassChecklist)) {
      errors.push('Invalid compass checklist structure');
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * Get import file template for users
   */
  public getImportTemplate(): ExportData {
    return {
      version: '1.0',
      exportDate: new Date().toISOString(),
      apiKeys: {
        openai: '',
        anthropic: '',
        google: '',
        openrouter: '',
      },
      modelSettings: {
        core: { model: 'gpt-4o', temperature: 0.7, maxOutputTokens: 2048 },
        plan: { model: 'claude-3-haiku-20240307', temperature: 0.8, maxOutputTokens: 2048 },
        compass: { model: 'gemini-1.5-flash', temperature: 0.5, maxOutputTokens: 4096, thinkingBudget: 8192 },
        general: { model: 'gpt-4o-mini', temperature: 0.7, maxOutputTokens: 2048 },
      },
      conversations: [],
      generalChatMessages: [],
      compassSummary: '',
      compassChecklist: [],
      appSettings: {
        activeConversationId: '',
        currentWorkspace: null,
      },
    };
  }

  /**
   * Download import template file
   */
  public downloadTemplate(): void {
    const template = this.getImportTemplate();
    const jsonString = JSON.stringify(template, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = 'tandem-cognition-import-template.json';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
}

export default ExportImportService.getInstance();
