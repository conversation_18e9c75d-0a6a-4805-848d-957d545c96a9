import { toast } from 'sonner';
import { ValidationResult } from './validationService';

export type NotificationType = 'success' | 'error' | 'warning' | 'info';

export interface NotificationOptions {
  title?: string;
  description?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

export class NotificationService {
  private static instance: NotificationService;
  
  private constructor() {}
  
  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  /**
   * Show a success notification
   */
  public success(message: string, options?: NotificationOptions): void {
    toast.success(message, {
      description: options?.description,
      duration: options?.duration || 4000,
      action: options?.action ? {
        label: options.action.label,
        onClick: options.action.onClick,
      } : undefined,
    });
  }

  /**
   * Show an error notification
   */
  public error(message: string, options?: NotificationOptions): void {
    toast.error(message, {
      description: options?.description,
      duration: options?.duration || 6000,
      action: options?.action ? {
        label: options.action.label,
        onClick: options.action.onClick,
      } : undefined,
    });
  }

  /**
   * Show a warning notification
   */
  public warning(message: string, options?: NotificationOptions): void {
    toast.warning(message, {
      description: options?.description,
      duration: options?.duration || 5000,
      action: options?.action ? {
        label: options.action.label,
        onClick: options.action.onClick,
      } : undefined,
    });
  }

  /**
   * Show an info notification
   */
  public info(message: string, options?: NotificationOptions): void {
    toast.info(message, {
      description: options?.description,
      duration: options?.duration || 4000,
      action: options?.action ? {
        label: options.action.label,
        onClick: options.action.onClick,
      } : undefined,
    });
  }

  /**
   * Show validation results as notifications
   */
  public showValidationResults(validation: ValidationResult, context?: string): void {
    const contextPrefix = context ? `${context}: ` : '';

    // Show errors
    validation.errors.forEach(error => {
      this.error(`${contextPrefix}${error}`);
    });

    // Show warnings
    validation.warnings.forEach(warning => {
      this.warning(`${contextPrefix}${warning}`);
    });

    // Show success if no errors
    if (validation.isValid && validation.errors.length === 0) {
      const successMessage = context 
        ? `${context} validation passed successfully`
        : 'Configuration is valid';
      
      this.success(successMessage, {
        description: validation.warnings.length > 0 
          ? `${validation.warnings.length} warning(s) found`
          : undefined
      });
    }
  }

  /**
   * Show API key validation results
   */
  public showApiKeyValidation(service: string, isValid: boolean, errors: string[], warnings: string[]): void {
    if (isValid) {
      this.success(`${service} API key is valid`, {
        description: warnings.length > 0 ? warnings.join(', ') : undefined
      });
    } else {
      this.error(`Invalid ${service} API key`, {
        description: errors.join(', ')
      });
    }
  }

  /**
   * Show configuration save results
   */
  public showSaveResult(success: boolean, error?: string): void {
    if (success) {
      this.success('Configuration saved successfully');
    } else {
      this.error('Failed to save configuration', {
        description: error || 'An unknown error occurred while saving'
      });
    }
  }

  /**
   * Show configuration load results
   */
  public showLoadResult(success: boolean, error?: string, warnings?: string[]): void {
    if (success) {
      this.success('Configuration loaded successfully', {
        description: warnings && warnings.length > 0 
          ? `${warnings.length} warning(s): ${warnings.join(', ')}`
          : undefined
      });
    } else {
      this.error('Failed to load configuration', {
        description: error || 'An unknown error occurred while loading'
      });
    }
  }

  /**
   * Show export results
   */
  public showExportResult(success: boolean, error?: string): void {
    if (success) {
      this.success('Data exported successfully', {
        description: 'Your backup file has been downloaded'
      });
    } else {
      this.error('Failed to export data', {
        description: error || 'An unknown error occurred during export'
      });
    }
  }

  /**
   * Show import results
   */
  public showImportResult(success: boolean, message: string, warnings?: string[]): void {
    if (success) {
      this.success('Data imported successfully', {
        description: warnings && warnings.length > 0 
          ? `${warnings.length} warning(s): ${warnings.join(', ')}`
          : message,
        action: {
          label: 'Refresh Page',
          onClick: () => window.location.reload()
        }
      });
    } else {
      this.error('Failed to import data', {
        description: message
      });
    }
  }

  /**
   * Show storage capacity warnings
   */
  public showStorageWarning(usagePercentage: number): void {
    if (usagePercentage > 90) {
      this.error('Storage almost full', {
        description: `Local storage is ${Math.round(usagePercentage)}% full. Consider exporting your data.`,
        action: {
          label: 'Export Data',
          onClick: () => {
            // This will be handled by the component that calls this
            console.log('Export data action triggered');
          }
        }
      });
    } else if (usagePercentage > 80) {
      this.warning('Storage getting full', {
        description: `Local storage is ${Math.round(usagePercentage)}% full. Consider exporting your data soon.`
      });
    }
  }

  /**
   * Show missing API key warnings
   */
  public showMissingApiKeyWarning(requiredService: string, modelName: string): void {
    this.warning(`${requiredService} API key required`, {
      description: `Model "${modelName}" requires a ${requiredService} API key. Please add it in settings.`,
      action: {
        label: 'Open Settings',
        onClick: () => {
          // This will be handled by the component that calls this
          console.log('Open settings action triggered');
        }
      }
    });
  }

  /**
   * Show environment variable detection
   */
  public showEnvironmentVariableDetected(services: string[]): void {
    this.info('Environment variables detected', {
      description: `Using API keys from environment variables: ${services.join(', ')}`,
      duration: 3000
    });
  }

  /**
   * Show configuration reset confirmation
   */
  public showResetConfirmation(onConfirm: () => void): void {
    this.warning('Reset all configuration?', {
      description: 'This will clear all API keys, settings, conversations, and chat history. This action cannot be undone.',
      duration: 10000,
      action: {
        label: 'Confirm Reset',
        onClick: onConfirm
      }
    });
  }

  /**
   * Dismiss all notifications
   */
  public dismissAll(): void {
    toast.dismiss();
  }

  /**
   * Show a loading notification that can be updated
   */
  public loading(message: string, id?: string): string {
    const toastId = toast.loading(message, { id });
    return toastId as string;
  }

  /**
   * Update a loading notification
   */
  public updateLoading(id: string, message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info'): void {
    switch (type) {
      case 'success':
        toast.success(message, { id });
        break;
      case 'error':
        toast.error(message, { id });
        break;
      case 'warning':
        toast.warning(message, { id });
        break;
      default:
        toast.info(message, { id });
        break;
    }
  }
}

export default NotificationService.getInstance();
