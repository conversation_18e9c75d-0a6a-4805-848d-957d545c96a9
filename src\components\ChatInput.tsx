
import React, { useRef } from 'react';
import { Send, Shield } from 'lucide-react';

interface ChatInputProps {
  inputValue: string;
  setInputValue: (value: string) => void;
  activeMode: 'core' | 'plan' | 'general';
  isLoading: boolean;
  isContextShielded?: boolean;
  onSubmit: (e: React.FormEvent) => void;
}

const ChatInput: React.FC<ChatInputProps> = ({
  inputValue,
  setInputValue,
  activeMode,
  isLoading,
  isContextShielded,
  onSubmit,
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputValue(e.target.value);
    
    // Auto-resize textarea
    const textarea = e.target;
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      onSubmit(e);
    }
  };

  return (
    <form onSubmit={onSubmit} className="p-4 sm:p-6 border-t border-border bg-card">
      <div className="flex gap-2 sm:gap-3">
        <textarea
          ref={textareaRef}
          value={inputValue}
          onChange={handleTextareaChange}
          onKeyDown={handleKeyDown}
          placeholder={`Ask ${activeMode === 'core' ? 'Core' : activeMode === 'plan' ? 'Plan' : 'General'} AI anything...`}
          className="flex-1 bg-background text-foreground placeholder-muted-foreground border border-input rounded-xl px-3 sm:px-4 py-2 sm:py-3 resize-none focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent text-sm sm:text-base transition-all duration-200"
          rows={1}
          disabled={isLoading}
        />
        <button
          type="submit"
          disabled={!inputValue.trim() || isLoading}
          className="px-4 sm:px-6 py-2 sm:py-3 bg-primary text-primary-foreground rounded-xl hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 shadow-soft"
        >
          <Send className="w-4 h-4 sm:w-5 sm:h-5" />
        </button>
      </div>
      <div className="flex items-center gap-2 sm:gap-3 mt-2 sm:mt-3 text-xs sm:text-sm text-muted-foreground">
        <div className={`w-2 h-2 rounded-full bg-primary`} />
        <span>Sending to {activeMode === 'core' ? 'Core' : activeMode === 'plan' ? 'Plan' : 'General'} AI</span>
        {activeMode !== 'general' && isContextShielded && (
          <div className="flex items-center gap-1 text-muted-foreground">
            <Shield className="w-3 h-3" />
            <span>Context Shielded</span>
          </div>
        )}
      </div>
    </form>
  );
};

export default ChatInput;
