import { AppState, ModelConfig } from '../stores/types';

// Environment variable names for API keys
export const ENV_VARS = {
  OPENAI_API_KEY: 'OPENAI_API_KEY',
  ANTHROPIC_API_KEY: 'ANTHROPIC_API_KEY', 
  GOOGLE_API_KEY: 'GOOGLE_API_KEY',
  OPENROUTER_API_KEY: 'OPENROUTER_API_KEY',
} as const;

// Local storage keys
export const STORAGE_KEYS = {
  API_KEYS: 'tandem-cognition-api-keys',
  MODEL_SETTINGS: 'tandem-cognition-model-settings',
  CONVERSATIONS: 'tandem-cognition-conversations',
  GENERAL_CHAT: 'tandem-cognition-general-chat',
  COMPASS_DATA: 'tandem-cognition-compass-data',
  APP_SETTINGS: 'tandem-cognition-app-settings',
} as const;

export interface ConfigurationSource {
  source: 'environment' | 'localStorage' | 'default' | 'user-input';
  isValid: boolean;
  lastUpdated?: Date;
}

export interface ApiKeyConfig {
  openai: { value: string; source: ConfigurationSource };
  anthropic: { value: string; source: ConfigurationSource };
  google: { value: string; source: ConfigurationSource };
  openrouter: { value: string; source: ConfigurationSource };
}

export interface StoredAppSettings {
  activeConversationId: string;
  currentWorkspace: AppState['currentWorkspace'];
  lastBackupDate?: Date;
}

export class ConfigurationService {
  private static instance: ConfigurationService;
  
  private constructor() {}
  
  public static getInstance(): ConfigurationService {
    if (!ConfigurationService.instance) {
      ConfigurationService.instance = new ConfigurationService();
    }
    return ConfigurationService.instance;
  }

  /**
   * Load API keys from environment variables first, then localStorage
   */
  public loadApiKeys(): ApiKeyConfig {
    const config: ApiKeyConfig = {
      openai: this.loadApiKey('openai', ENV_VARS.OPENAI_API_KEY),
      anthropic: this.loadApiKey('anthropic', ENV_VARS.ANTHROPIC_API_KEY),
      google: this.loadApiKey('google', ENV_VARS.GOOGLE_API_KEY),
      openrouter: this.loadApiKey('openrouter', ENV_VARS.OPENROUTER_API_KEY),
    };

    return config;
  }

  private loadApiKey(service: keyof AppState['apiKeys'], envVar: string): { value: string; source: ConfigurationSource } {
    // First try environment variable
    const envValue = this.getEnvironmentVariable(envVar);
    if (envValue) {
      return {
        value: envValue,
        source: {
          source: 'environment',
          isValid: this.validateApiKey(service, envValue),
          lastUpdated: new Date(),
        }
      };
    }

    // Then try localStorage
    const storedKeys = this.getStoredApiKeys();
    const storedValue = storedKeys[service];
    if (storedValue) {
      return {
        value: storedValue,
        source: {
          source: 'localStorage',
          isValid: this.validateApiKey(service, storedValue),
          lastUpdated: new Date(),
        }
      };
    }

    // Return empty with default source
    return {
      value: '',
      source: {
        source: 'default',
        isValid: false,
      }
    };
  }

  /**
   * Get environment variable (works in development with Vite)
   */
  private getEnvironmentVariable(name: string): string | undefined {
    // In Vite, environment variables are available via import.meta.env
    // and must be prefixed with VITE_ to be exposed to the client
    const viteEnvName = `VITE_${name}`;
    return (import.meta.env as any)[viteEnvName] || undefined;
  }

  /**
   * Basic API key validation
   */
  public validateApiKey(service: keyof AppState['apiKeys'], key: string): boolean {
    if (!key || key.trim().length === 0) return false;

    switch (service) {
      case 'openai':
        return key.startsWith('sk-') && key.length > 20;
      case 'anthropic':
        return key.startsWith('sk-ant-') && key.length > 20;
      case 'google':
        return key.startsWith('AIza') && key.length > 20;
      case 'openrouter':
        return key.startsWith('sk-or-') && key.length > 20;
      default:
        return false;
    }
  }

  /**
   * Save API keys to localStorage
   */
  public saveApiKeys(apiKeys: AppState['apiKeys']): void {
    try {
      const timestamp = new Date().toISOString();
      const dataToStore = {
        ...apiKeys,
        _metadata: {
          lastUpdated: timestamp,
          version: '1.0',
        }
      };
      localStorage.setItem(STORAGE_KEYS.API_KEYS, JSON.stringify(dataToStore));
    } catch (error) {
      console.error('Failed to save API keys to localStorage:', error);
      throw new Error('Failed to save API keys. Local storage may be full or unavailable.');
    }
  }

  /**
   * Get stored API keys from localStorage
   */
  private getStoredApiKeys(): AppState['apiKeys'] {
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.API_KEYS);
      if (!stored) return { openai: '', anthropic: '', google: '', openrouter: '' };
      
      const parsed = JSON.parse(stored);
      return {
        openai: parsed.openai || '',
        anthropic: parsed.anthropic || '',
        google: parsed.google || '',
        openrouter: parsed.openrouter || '',
      };
    } catch (error) {
      console.error('Failed to load API keys from localStorage:', error);
      return { openai: '', anthropic: '', google: '', openrouter: '' };
    }
  }

  /**
   * Save model settings to localStorage
   */
  public saveModelSettings(modelSettings: AppState['modelSettings']): void {
    try {
      const timestamp = new Date().toISOString();
      const dataToStore = {
        ...modelSettings,
        _metadata: {
          lastUpdated: timestamp,
          version: '1.0',
        }
      };
      localStorage.setItem(STORAGE_KEYS.MODEL_SETTINGS, JSON.stringify(dataToStore));
    } catch (error) {
      console.error('Failed to save model settings to localStorage:', error);
      throw new Error('Failed to save model settings. Local storage may be full or unavailable.');
    }
  }

  /**
   * Load model settings from localStorage
   */
  public loadModelSettings(): AppState['modelSettings'] | null {
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.MODEL_SETTINGS);
      if (!stored) return null;
      
      const parsed = JSON.parse(stored);
      // Remove metadata before returning
      const { _metadata, ...modelSettings } = parsed;
      return modelSettings as AppState['modelSettings'];
    } catch (error) {
      console.error('Failed to load model settings from localStorage:', error);
      return null;
    }
  }

  /**
   * Save conversations to localStorage
   */
  public saveConversations(conversations: AppState['conversations']): void {
    try {
      const timestamp = new Date().toISOString();
      const dataToStore = {
        conversations,
        _metadata: {
          lastUpdated: timestamp,
          version: '1.0',
          count: conversations.length,
        }
      };
      localStorage.setItem(STORAGE_KEYS.CONVERSATIONS, JSON.stringify(dataToStore));
    } catch (error) {
      console.error('Failed to save conversations to localStorage:', error);
      throw new Error('Failed to save conversations. Local storage may be full or unavailable.');
    }
  }

  /**
   * Load conversations from localStorage
   */
  public loadConversations(): AppState['conversations'] | null {
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.CONVERSATIONS);
      if (!stored) return null;
      
      const parsed = JSON.parse(stored);
      return parsed.conversations || null;
    } catch (error) {
      console.error('Failed to load conversations from localStorage:', error);
      return null;
    }
  }

  /**
   * Save general chat messages to localStorage
   */
  public saveGeneralChat(messages: AppState['generalChatMessages']): void {
    try {
      const timestamp = new Date().toISOString();
      const dataToStore = {
        messages,
        _metadata: {
          lastUpdated: timestamp,
          version: '1.0',
          count: messages.length,
        }
      };
      localStorage.setItem(STORAGE_KEYS.GENERAL_CHAT, JSON.stringify(dataToStore));
    } catch (error) {
      console.error('Failed to save general chat to localStorage:', error);
      throw new Error('Failed to save general chat. Local storage may be full or unavailable.');
    }
  }

  /**
   * Load general chat messages from localStorage
   */
  public loadGeneralChat(): AppState['generalChatMessages'] | null {
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.GENERAL_CHAT);
      if (!stored) return null;
      
      const parsed = JSON.parse(stored);
      return parsed.messages || null;
    } catch (error) {
      console.error('Failed to load general chat from localStorage:', error);
      return null;
    }
  }

  /**
   * Save compass data to localStorage
   */
  public saveCompassData(summary: string, checklist: AppState['compassChecklist']): void {
    try {
      const timestamp = new Date().toISOString();
      const dataToStore = {
        summary,
        checklist,
        _metadata: {
          lastUpdated: timestamp,
          version: '1.0',
        }
      };
      localStorage.setItem(STORAGE_KEYS.COMPASS_DATA, JSON.stringify(dataToStore));
    } catch (error) {
      console.error('Failed to save compass data to localStorage:', error);
      throw new Error('Failed to save compass data. Local storage may be full or unavailable.');
    }
  }

  /**
   * Load compass data from localStorage
   */
  public loadCompassData(): { summary: string; checklist: AppState['compassChecklist'] } | null {
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.COMPASS_DATA);
      if (!stored) return null;
      
      const parsed = JSON.parse(stored);
      return {
        summary: parsed.summary || '',
        checklist: parsed.checklist || [],
      };
    } catch (error) {
      console.error('Failed to load compass data from localStorage:', error);
      return null;
    }
  }

  /**
   * Save app settings to localStorage
   */
  public saveAppSettings(settings: StoredAppSettings): void {
    try {
      const timestamp = new Date().toISOString();
      const dataToStore = {
        ...settings,
        _metadata: {
          lastUpdated: timestamp,
          version: '1.0',
        }
      };
      localStorage.setItem(STORAGE_KEYS.APP_SETTINGS, JSON.stringify(dataToStore));
    } catch (error) {
      console.error('Failed to save app settings to localStorage:', error);
      throw new Error('Failed to save app settings. Local storage may be full or unavailable.');
    }
  }

  /**
   * Load app settings from localStorage
   */
  public loadAppSettings(): StoredAppSettings | null {
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.APP_SETTINGS);
      if (!stored) return null;
      
      const parsed = JSON.parse(stored);
      const { _metadata, ...settings } = parsed;
      return settings as StoredAppSettings;
    } catch (error) {
      console.error('Failed to load app settings from localStorage:', error);
      return null;
    }
  }

  /**
   * Clear all stored configuration data
   */
  public clearAllData(): void {
    try {
      Object.values(STORAGE_KEYS).forEach(key => {
        localStorage.removeItem(key);
      });
    } catch (error) {
      console.error('Failed to clear configuration data:', error);
      throw new Error('Failed to clear configuration data.');
    }
  }

  /**
   * Check if localStorage is available
   */
  public isStorageAvailable(): boolean {
    try {
      const test = '__storage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get storage usage information
   */
  public getStorageInfo(): { used: number; available: boolean; keys: string[] } {
    if (!this.isStorageAvailable()) {
      return { used: 0, available: false, keys: [] };
    }

    let used = 0;
    const keys: string[] = [];
    
    try {
      Object.values(STORAGE_KEYS).forEach(key => {
        const item = localStorage.getItem(key);
        if (item) {
          used += item.length;
          keys.push(key);
        }
      });
    } catch (error) {
      console.error('Failed to calculate storage usage:', error);
    }

    return { used, available: true, keys };
  }
}

export default ConfigurationService.getInstance();
