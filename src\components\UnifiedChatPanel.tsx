
import React, { useState } from 'react';
import { MessageSquare } from 'lucide-react';
import useAppStore from '../stores/useAppStore';
import MessageList from './MessageList';
import ChatInput from './ChatInput';
import ChatActions from './ChatActions';
import BranchModal from './BranchModal';
import PromptDraftModal from './PromptDraftModal';
import { useToast } from '../hooks/use-toast';
import ChatHeader from './ChatHeader';

const UnifiedChatPanel: React.FC = () => {
  const [inputValue, setInputValue] = useState('');
  const [activeMode, setActiveMode] = useState<'core' | 'plan' | 'general'>('core');
  const [editingMessageId, setEditingMessageId] = useState<string | null>(null);
  const [editingContent, setEditingContent] = useState('');
  const [isPromptDraftModalOpen, setIsPromptDraftModalOpen] = useState(false);
  const [isGeneratingDraft, setIsGeneratingDraft] = useState(false);
  const [generatedDraft, setGeneratedDraft] = useState('');
  const { toast } = useToast();
  
  const { 
    isLoading, 
    sendMessage, 
    sendGeneralChatMessage,
    clearGeneralChat,
    togglePinMessage, 
    getActiveConversation,
    generalChatMessages,
    deleteMessage,
    editMessage,
    refreshAssistantMessage,
    resendUserMessage,
    toggleBranchModal,
    toggleContextShield,
    getCoreContextForPlan
  } = useAppStore();
  
  const activeConversation = getActiveConversation();
  
  // Get messages based on active mode - Plan and Core now have separate histories
  const getMessagesToDisplay = () => {
    if (activeMode === 'general') {
      return generalChatMessages.map(msg => ({ ...msg, mode: 'general' as const }));
    } else if (activeMode === 'core') {
      return activeConversation ? activeConversation.messages.core.map(msg => ({ ...msg, mode: 'core' as const })) : [];
    } else if (activeMode === 'plan') {
      return activeConversation ? activeConversation.messages.plan.map(msg => ({ ...msg, mode: 'plan' as const })) : [];
    }
    return [];
  };

  const messagesToDisplay = getMessagesToDisplay();
  const loading = isLoading[activeMode];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputValue.trim() && !loading) {
      if (activeMode === 'general') {
        sendGeneralChatMessage(inputValue.trim());
      } else if (activeConversation) {
        sendMessage(activeMode, inputValue.trim());
      }
      setInputValue('');
    }
  };

  const handleCopyMessage = () => {
    toast({
      title: "Copied!",
      description: "Message copied to clipboard",
    });
  };

  const handleEditMessage = (messageId: string, content: string) => {
    setEditingMessageId(messageId);
    setEditingContent(content);
  };

  const handleSaveEdit = () => {
    if (editingMessageId && editingContent.trim()) {
      editMessage(activeMode, editingMessageId, editingContent.trim());
      setEditingMessageId(null);
      setEditingContent('');
      toast({
        title: "Updated!",
        description: "Message has been updated",
      });
    }
  };

  const handleCancelEdit = () => {
    setEditingMessageId(null);
    setEditingContent('');
  };

  const handleBranchConversation = () => {
    if (activeMode === 'core' && activeConversation && activeConversation.messages.core.length > 0) {
      toggleBranchModal();
    } else {
      toast({
        title: "Cannot branch",
        description: "You can only branch from Core mode with existing messages",
        variant: "destructive"
      });
    }
  };

  const handleToggleContextShield = () => {
    if (activeConversation) {
      toggleContextShield(activeConversation.id);
      toast({
        title: activeConversation.isContextShielded ? "Context Unshielded" : "Context Shielded",
        description: activeConversation.isContextShielded 
          ? "This conversation is now visible to Compass & Plan AI"
          : "This conversation is now hidden from Compass & Plan AI"
      });
    }
  };

  const handleCreatePromptDraft = async () => {
    if (activeMode !== 'plan' || !activeConversation) return;

    const planMessages = activeConversation.messages.plan;

    if (planMessages.length === 0) {
      toast({
        title: "No discussion yet",
        description: "Have a discussion with Plan AI first to generate a prompt draft.",
        variant: "destructive"
      });
      return;
    }

    setIsGeneratingDraft(true);
    setGeneratedDraft(''); // Clear previous draft

    try {
      // Get the plan conversation context
      const planContext = planMessages.slice(-10).map(msg =>
        `${msg.role}: ${msg.content}`
      ).join('\n\n');

      const promptGenerationRequest = `Based on the following planning conversation, generate a focused and detailed prompt that can be sent to a Core AI assistant for implementation or analysis.

Planning conversation context:
${planContext}

Please create a comprehensive prompt that:
1. Clearly states the objective
2. Provides necessary context
3. Specifies expected deliverables
4. Is actionable and specific

Return only the generated prompt, ready to be sent to the Core AI.`;

      // Use the plan model settings to generate the draft
      const { modelSettings, apiKeys } = useAppStore.getState();
      const planSettings = modelSettings.plan;
      const model = planSettings.model;

      let generatedPrompt = '';

      // Use the same API integration logic as in messageActions
      if (apiKeys.openrouter && (model.includes('/') || model.startsWith('deepseek'))) {
        const OpenAI = (await import('openai')).default;
        const openai = new OpenAI({
          baseURL: 'https://openrouter.ai/api/v1',
          apiKey: apiKeys.openrouter,
        });

        const response = await openai.chat.completions.create({
          model: model,
          messages: [{ role: 'user', content: promptGenerationRequest }],
          temperature: 0.7,
          max_tokens: 1000,
        });

        generatedPrompt = response.choices[0]?.message?.content || 'Could not generate prompt.';
      } else if (model.startsWith('gemini') && apiKeys.google) {
        const { GoogleGenerativeAI } = await import('@google/generative-ai');
        const genAI = new GoogleGenerativeAI(apiKeys.google);
        const geminiModel = genAI.getGenerativeModel({
          model,
          generationConfig: { temperature: 0.7, maxOutputTokens: 1000 }
        });
        const result = await geminiModel.generateContent(promptGenerationRequest);
        generatedPrompt = (await result.response).text();
      } else if (model.startsWith('gpt') && apiKeys.openai) {
        const OpenAI = (await import('openai')).default;
        const openai = new OpenAI({ apiKey: apiKeys.openai });
        const response = await openai.chat.completions.create({
          model: model,
          messages: [{ role: 'user', content: promptGenerationRequest }],
          temperature: 0.7,
          max_tokens: 1000,
        });
        generatedPrompt = response.choices[0]?.message?.content || 'Could not generate prompt.';
      } else if (model.startsWith('claude') && apiKeys.anthropic) {
        const Anthropic = (await import('@anthropic-ai/sdk')).default;
        const anthropic = new Anthropic({ apiKey: apiKeys.anthropic });
        const response = await anthropic.messages.create({
          model: model,
          max_tokens: 1000,
          temperature: 0.7,
          messages: [{ role: 'user', content: promptGenerationRequest }],
        });
        generatedPrompt = response.content[0]?.type === 'text' ? response.content[0].text : 'Could not generate prompt.';
      } else {
        // Fallback when no API key is configured
        const lastUserMessage = [...planMessages].reverse().find(m => m.role === 'user');
        generatedPrompt = lastUserMessage
          ? `Based on our planning discussion, please perform the following analysis: ${lastUserMessage.content}`
          : "Based on our planning discussion, please perform a detailed analysis.";
      }

      setGeneratedDraft(generatedPrompt);
      setIsPromptDraftModalOpen(true);
    } catch (error) {
      console.error("Failed to generate prompt draft", error);
      toast({
        title: "Error",
        description: "Could not generate prompt draft. Please check your API configuration.",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingDraft(false);
    }
  };

  const getCoreContextInfo = () => {
    if (activeMode === 'plan') {
      const coreMessages = getCoreContextForPlan();
      return coreMessages.length > 0 ? `${coreMessages.length} Core messages available` : 'No Core context available';
    }
    return '';
  };

  if (!activeConversation && activeMode !== 'general') {
    return (
      <div className="flex flex-col h-full bg-card border border-border rounded-2xl overflow-hidden shadow-soft">
        <div className="flex items-center justify-center h-full text-muted-foreground">
          <div className="text-center px-8">
            <div className="w-16 h-16 bg-muted rounded-2xl flex items-center justify-center mx-auto mb-6">
              <MessageSquare className="w-8 h-8 text-muted-foreground" />
            </div>
            <h3 className="text-xl font-semibold mb-3">No conversation selected</h3>
            <p className="text-base text-muted-foreground">Create a new conversation to get started</p>
          </div>
        </div>
        <BranchModal />
        <PromptDraftModal 
          isOpen={isPromptDraftModalOpen} 
          onClose={() => setIsPromptDraftModalOpen(false)} 
        />
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-card border border-border rounded-2xl overflow-hidden shadow-soft">
      <div className="bg-card px-4 py-3 border-b border-border">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <ChatHeader
              activeMode={activeMode}
              setActiveMode={setActiveMode}
              activeConversation={activeConversation}
              generalChatMessagesCount={generalChatMessages.length}
              coreMessagesCount={activeConversation?.messages.core.length ?? 0}
              planMessagesCount={activeConversation?.messages.plan.length ?? 0}
              getCoreContextInfo={getCoreContextInfo}
              handleCreatePromptDraft={handleCreatePromptDraft}
              isGeneratingDraft={isGeneratingDraft}
              handleToggleContextShield={handleToggleContextShield}
              handleBranchConversation={handleBranchConversation}
              clearGeneralChat={clearGeneralChat}
            />
          </div>
          <ChatActions
            activeMode={activeMode}
            activeConversation={activeConversation}
          />
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 sm:p-6 space-y-4 sm:space-y-6">
        <MessageList
          messages={messagesToDisplay}
          activeMode={activeMode}
          isLoading={loading}
          editingMessageId={editingMessageId}
          editingContent={editingContent}
          setEditingContent={setEditingContent}
          onTogglePin={(messageId) => {
            if (activeMode === 'core' || activeMode === 'plan') {
              togglePinMessage(activeMode, messageId);
            }
          }}
          onCopyMessage={handleCopyMessage}
          onRefreshMessage={(messageId) => refreshAssistantMessage(activeMode, messageId)}
          onDeleteMessage={(messageId) => deleteMessage(activeMode, messageId)}
          onEditMessage={handleEditMessage}
          onResendMessage={(messageId) => resendUserMessage(activeMode, messageId)}
          onSaveEdit={handleSaveEdit}
          onCancelEdit={handleCancelEdit}
        />
      </div>

      <ChatInput
        inputValue={inputValue}
        setInputValue={setInputValue}
        activeMode={activeMode}
        isLoading={loading}
        isContextShielded={activeConversation?.isContextShielded}
        onSubmit={handleSubmit}
      />

      <BranchModal />
      <PromptDraftModal 
        isOpen={isPromptDraftModalOpen} 
        onClose={() => setIsPromptDraftModalOpen(false)} 
        initialPrompt={generatedDraft}
      />
    </div>
  );
};

export default UnifiedChatPanel;
