import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CheckCircle, AlertTriangle, XCircle, Settings, Shield } from 'lucide-react';
import { ConfigurationStatus } from '@/hooks/useConfigurationStatus';
import useAppStore from '@/stores/useAppStore';

interface ConfigurationStatusIndicatorProps {
  status: ConfigurationStatus;
  variant?: 'compact' | 'detailed' | 'badge-only';
  showPopover?: boolean;
  className?: string;
}

const ConfigurationStatusIndicator: React.FC<ConfigurationStatusIndicatorProps> = ({
  status,
  variant = 'compact',
  showPopover = true,
  className = '',
}) => {
  const { toggleSettingsModal } = useAppStore();

  const getStatusIcon = () => {
    switch (status.overallHealth) {
      case 'healthy':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
    }
  };

  const getStatusBadge = () => {
    switch (status.overallHealth) {
      case 'healthy':
        return (
          <Badge className="bg-green-900 text-green-100 border-green-700">
            <CheckCircle className="w-3 h-3 mr-1" />
            Ready
          </Badge>
        );
      case 'warning':
        return (
          <Badge className="bg-yellow-900 text-yellow-100 border-yellow-700">
            <AlertTriangle className="w-3 h-3 mr-1" />
            Warning
          </Badge>
        );
      case 'error':
        return (
          <Badge variant="destructive" className="bg-red-900 text-red-100 border-red-700">
            <XCircle className="w-3 h-3 mr-1" />
            Issues
          </Badge>
        );
    }
  };

  const getStatusText = () => {
    switch (status.overallHealth) {
      case 'healthy':
        return 'Configuration is healthy';
      case 'warning':
        return `${status.warnings.length} warning(s)`;
      case 'error':
        return `${status.criticalIssues.length} critical issue(s)`;
    }
  };

  const PopoverContentComponent = () => (
    <PopoverContent className="w-80 bg-gray-900 border-gray-700 text-white">
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h4 className="font-medium">Configuration Status</h4>
          {getStatusBadge()}
        </div>

        {/* Critical Issues */}
        {status.criticalIssues.length > 0 && (
          <div>
            <p className="text-sm font-medium text-red-400 mb-2">Critical Issues:</p>
            <ul className="text-sm text-red-300 space-y-1">
              {status.criticalIssues.map((issue, index) => (
                <li key={index} className="flex items-start gap-2">
                  <XCircle className="w-3 h-3 mt-0.5 flex-shrink-0" />
                  {issue}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Warnings */}
        {status.warnings.length > 0 && (
          <div>
            <p className="text-sm font-medium text-yellow-400 mb-2">Warnings:</p>
            <ul className="text-sm text-yellow-300 space-y-1">
              {status.warnings.slice(0, 3).map((warning, index) => (
                <li key={index} className="flex items-start gap-2">
                  <AlertTriangle className="w-3 h-3 mt-0.5 flex-shrink-0" />
                  {warning}
                </li>
              ))}
              {status.warnings.length > 3 && (
                <li className="text-xs text-gray-400">
                  +{status.warnings.length - 3} more warnings
                </li>
              )}
            </ul>
          </div>
        )}

        {/* Environment Variables */}
        {status.environmentApiKeys.length > 0 && (
          <div>
            <p className="text-sm font-medium text-blue-400 mb-2">Environment Variables:</p>
            <div className="flex flex-wrap gap-1">
              {status.environmentApiKeys.map((service, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="border-blue-500 text-blue-300 text-xs"
                >
                  <Shield className="w-3 h-3 mr-1" />
                  {service}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Healthy Status */}
        {status.overallHealth === 'healthy' && (
          <div className="text-sm text-green-300">
            <CheckCircle className="w-4 h-4 inline mr-2" />
            All configurations are valid and ready to use.
          </div>
        )}

        <div className="pt-2 border-t border-gray-700">
          <Button
            onClick={toggleSettingsModal}
            size="sm"
            className="w-full bg-blue-600 hover:bg-blue-700"
          >
            <Settings className="w-4 h-4 mr-2" />
            Open Settings
          </Button>
        </div>
      </div>
    </PopoverContent>
  );

  if (variant === 'badge-only') {
    return (
      <div className={className}>
        {showPopover ? (
          <Popover>
            <PopoverTrigger asChild>
              <button className="cursor-pointer">
                {getStatusBadge()}
              </button>
            </PopoverTrigger>
            <PopoverContentComponent />
          </Popover>
        ) : (
          getStatusBadge()
        )}
      </div>
    );
  }

  if (variant === 'detailed') {
    return (
      <div className={`flex items-center gap-3 p-3 bg-gray-800 rounded-lg border border-gray-700 ${className}`}>
        {getStatusIcon()}
        <div className="flex-1">
          <p className="text-sm font-medium text-white">{getStatusText()}</p>
          {status.criticalIssues.length > 0 && (
            <p className="text-xs text-red-400">{status.criticalIssues[0]}</p>
          )}
          {status.criticalIssues.length === 0 && status.warnings.length > 0 && (
            <p className="text-xs text-yellow-400">{status.warnings[0]}</p>
          )}
        </div>
        <Button
          onClick={toggleSettingsModal}
          size="sm"
          variant="outline"
          className="border-gray-600 text-gray-300 hover:bg-gray-700"
        >
          <Settings className="w-4 h-4 mr-2" />
          Settings
        </Button>
      </div>
    );
  }

  // Compact variant
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {showPopover ? (
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-2 hover:bg-gray-800"
            >
              {getStatusIcon()}
              <span className="ml-2 text-sm">{getStatusText()}</span>
            </Button>
          </PopoverTrigger>
          <PopoverContentComponent />
        </Popover>
      ) : (
        <div className="flex items-center gap-2">
          {getStatusIcon()}
          <span className="text-sm text-gray-300">{getStatusText()}</span>
        </div>
      )}
    </div>
  );
};

export default ConfigurationStatusIndicator;
