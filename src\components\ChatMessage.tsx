
import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import rehypeHighlight from 'rehype-highlight';
import { Pin, PinOff, <PERSON><PERSON>, Refresh<PERSON>cw, Trash2, Edit, Send } from 'lucide-react';
import { Message } from '../stores/useAppStore';
import { Button } from './ui/button';
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
} from './ui/context-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from './ui/tooltip';

interface ChatMessageProps {
  message: Message;
  onPin: () => void;
  onCopy?: () => void;
  onRefresh?: () => void;
  onDelete?: () => void;
  onEdit?: () => void;
  onResend?: () => void;
}

const ChatMessage: React.FC<ChatMessageProps> = ({ 
  message, 
  onPin, 
  onCopy,
  onRefresh,
  onDelete,
  onEdit,
  onResend
}) => {
  const isUser = message.role === 'user';
  const [isHovered, setIsHovered] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const handleCopy = () => {
    navigator.clipboard.writeText(message.content);
    onCopy?.();
  };

  const handleDelete = () => {
    if (showDeleteConfirm) {
      onDelete?.();
      setShowDeleteConfirm(false);
    } else {
      setShowDeleteConfirm(true);
      setTimeout(() => setShowDeleteConfirm(false), 3000);
    }
  };

  return (
    <TooltipProvider>
      <ContextMenu>
        <ContextMenuTrigger asChild>
          <div
            className={`flex mb-4 ${isUser ? 'justify-end' : 'justify-start'}`}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            <div
              className={`max-w-[80%] rounded-lg px-4 py-3 relative group ${
                isUser
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-muted text-muted-foreground'
              } ${message.isPinned ? 'ring-2 ring-yellow-400' : ''}`}
            >
              <div className="flex items-start justify-between gap-2">
                <div className="flex-1">
                  <ReactMarkdown
                    rehypePlugins={[rehypeHighlight]}
                    components={{
                      p: ({ children }) => <p className="mb-2 last:mb-0">{children}</p>,
                      code: ({ children, className, ...props }) => {
                        const isInline = !className;
                        return isInline ? (
                          <code className="bg-accent px-1 py-0.5 rounded text-sm" {...props}>
                            {children}
                          </code>
                        ) : (
                          <code className="block bg-accent p-2 rounded text-sm overflow-x-auto" {...props}>
                            {children}
                          </code>
                        );
                      },
                      h1: ({ children }) => <h1 className="text-lg font-bold mb-2">{children}</h1>,
                      h2: ({ children }) => <h2 className="text-base font-semibold mb-2">{children}</h2>,
                      h3: ({ children }) => <h3 className="text-sm font-medium mb-1">{children}</h3>,
                      ul: ({ children }) => <ul className="list-disc list-inside mb-2 space-y-1">{children}</ul>,
                      ol: ({ children }) => <ol className="list-decimal list-inside mb-2 space-y-1">{children}</ol>,
                      li: ({ children }) => <li className="text-sm">{children}</li>,
                    }}
                  >
                    {message.content}
                  </ReactMarkdown>
                </div>

                {/* Action buttons - visible on hover */}
                {(isHovered || message.isPinned) && (
                  <div className="flex items-center gap-1 ml-2">
                    {/* Pin button for non-user messages */}
                    {!isUser && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6 hover:bg-accent"
                            onClick={onPin}
                          >
                            {message.isPinned ? (
                              <Pin className="w-3 h-3 text-yellow-400" />
                            ) : (
                              <PinOff className="w-3 h-3" />
                            )}
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          {message.isPinned ? 'Unpin message' : 'Pin message'}
                        </TooltipContent>
                      </Tooltip>
                    )}

                    {/* Copy button */}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6 hover:bg-accent"
                          onClick={handleCopy}
                        >
                          <Copy className="w-3 h-3" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>Copy message</TooltipContent>
                    </Tooltip>

                    {/* User message actions */}
                    {isUser && (
                      <>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6 hover:bg-accent"
                              onClick={onEdit}
                            >
                              <Edit className="w-3 h-3" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>Edit message</TooltipContent>
                        </Tooltip>

                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6 hover:bg-accent"
                              onClick={onResend}
                            >
                              <Send className="w-3 h-3" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>Resend message</TooltipContent>
                        </Tooltip>
                      </>
                    )}

                    {/* AI message refresh */}
                    {!isUser && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6 hover:bg-accent"
                            onClick={onRefresh}
                          >
                            <RefreshCcw className="w-3 h-3" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Refresh response</TooltipContent>
                      </Tooltip>
                    )}

                    {/* Delete button */}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className={`h-6 w-6 hover:bg-accent ${
                            showDeleteConfirm ? 'bg-destructive/20' : ''
                          }`}
                          onClick={handleDelete}
                        >
                          <Trash2 className={`w-3 h-3 ${
                            showDeleteConfirm ? 'text-destructive' : ''
                          }`} />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        {showDeleteConfirm ? 'Click again to confirm' : 'Delete message'}
                      </TooltipContent>
                    </Tooltip>
                  </div>
                )}
              </div>

              <div className="text-xs text-muted-foreground mt-2">
                {message.timestamp.toLocaleTimeString()}
              </div>
            </div>
          </div>
        </ContextMenuTrigger>

        <ContextMenuContent>
          <ContextMenuItem onClick={handleCopy}>
            <Copy className="w-4 h-4 mr-2" />
            Copy message
          </ContextMenuItem>
          
          {!isUser && (
            <>
              <ContextMenuItem onClick={onPin}>
                {message.isPinned ? (
                  <>
                    <PinOff className="w-4 h-4 mr-2" />
                    Unpin message
                  </>
                ) : (
                  <>
                    <Pin className="w-4 h-4 mr-2" />
                    Pin message
                  </>
                )}
              </ContextMenuItem>
              <ContextMenuItem onClick={onRefresh}>
                <RefreshCcw className="w-4 h-4 mr-2" />
                Refresh response
              </ContextMenuItem>
            </>
          )}

          {isUser && (
            <>
              <ContextMenuItem onClick={onEdit}>
                <Edit className="w-4 h-4 mr-2" />
                Edit message
              </ContextMenuItem>
              <ContextMenuItem onClick={onResend}>
                <Send className="w-4 h-4 mr-2" />
                Resend message
              </ContextMenuItem>
            </>
          )}

          <ContextMenuItem onClick={handleDelete} className="text-destructive">
            <Trash2 className="w-4 h-4 mr-2" />
            Delete message
          </ContextMenuItem>
        </ContextMenuContent>
      </ContextMenu>
    </TooltipProvider>
  );
};

export default ChatMessage;
