
import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Download, Upload, RotateCcw, Shield, AlertTriangle, CheckCircle } from 'lucide-react';
import useAppStore from '../stores/useAppStore';
import ApiKeySettings from './settings/ApiKeySettings';
import ModelSettingsPanel from './settings/ModelSettingsPanel';
import ConfigurationStatus from './settings/ConfigurationStatus';
import DataManagement from './settings/DataManagement';
import validationService from '../services/validationService';
import exportImportService from '../services/exportImportService';
import notificationService from '../services/notificationService';

const SettingsModal: React.FC = () => {
  const {
    isSettingsModalOpen,
    toggleSettingsModal,
    apiKeys,
    modelSettings,
    updateApiKey,
    updateModelParameter,
    clearAllConfiguration,
    saveConfigurationToStorage,
    validateApiKey,
  } = useAppStore();

  const [activeTab, setActiveTab] = useState('api-keys');
  const [configValidation, setConfigValidation] = useState(validationService.validateConfiguration({
    apiKeys,
    modelSettings,
    conversations: [],
    generalChatMessages: [],
    compassSummary: '',
    compassChecklist: [],
    isLoading: { core: false, plan: false, compass: false, general: false },
    isSettingsModalOpen: false,
    isBranchModalOpen: false,
    branchTitle: '',
    currentWorkspace: null,
    draftPrompt: '',
    activeConversationId: '',
  }));

  const modelOptions = {
    openai: ['gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo', 'gpt-3.5-turbo'],
    anthropic: ['claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307'],
    google: ['gemini-1.5-pro', 'gemini-1.5-flash', 'gemini-pro'],
    openrouter: [
      'openai/gpt-4o',
      'openai/gpt-4o-mini',
      'anthropic/claude-3-opus',
      'anthropic/claude-3-sonnet',
      'anthropic/claude-3-haiku',
      'google/gemini-pro-1.5',
      'meta-llama/llama-3.1-405b-instruct',
      'meta-llama/llama-3.1-70b-instruct',
      'mistralai/mixtral-8x7b-instruct'
    ],
  };

  const allModels = [...modelOptions.openai, ...modelOptions.anthropic, ...modelOptions.google, ...modelOptions.openrouter];

  const handleExportData = async () => {
    try {
      const appState = useAppStore.getState();
      await exportImportService.exportData(appState);
      notificationService.showExportResult(true);
    } catch (error) {
      notificationService.showExportResult(false, error instanceof Error ? error.message : 'Unknown error');
    }
  };

  const handleImportData = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const result = await exportImportService.importData(file);
      notificationService.showImportResult(result.success, result.message, result.warnings);
    } catch (error) {
      notificationService.showImportResult(false, error instanceof Error ? error.message : 'Unknown error');
    }

    // Reset the input
    event.target.value = '';
  };

  return (
    <Dialog open={isSettingsModalOpen} onOpenChange={toggleSettingsModal}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] bg-gray-900 text-white border-gray-700 p-0">
        <DialogHeader className="px-6 pt-6 pb-2">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl font-semibold">Settings</DialogTitle>
            <div className="flex items-center gap-2">
              {configValidation.isValid ? (
                <Badge variant="secondary" className="bg-green-900 text-green-100">
                  <CheckCircle className="w-3 h-3 mr-1" />
                  Valid
                </Badge>
              ) : (
                <Badge variant="destructive" className="bg-red-900 text-red-100">
                  <AlertTriangle className="w-3 h-3 mr-1" />
                  Issues
                </Badge>
              )}
            </div>
          </div>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
          <div className="px-6">
            <TabsList className="grid w-full grid-cols-4 bg-gray-800">
              <TabsTrigger value="api-keys" className="data-[state=active]:bg-blue-600">
                API Keys
              </TabsTrigger>
              <TabsTrigger value="models" className="data-[state=active]:bg-blue-600">
                Models
              </TabsTrigger>
              <TabsTrigger value="status" className="data-[state=active]:bg-blue-600">
                Status
              </TabsTrigger>
              <TabsTrigger value="data" className="data-[state=active]:bg-blue-600">
                Data
              </TabsTrigger>
            </TabsList>
          </div>

          <ScrollArea className="max-h-[calc(90vh-12rem)] px-6 pb-6">
            <TabsContent value="api-keys" className="mt-6">
              <ApiKeySettings apiKeys={apiKeys} updateApiKey={updateApiKey} />
            </TabsContent>

            <TabsContent value="models" className="mt-6">
              <div>
                <h3 className="text-lg font-semibold mb-4 text-blue-400">Model Settings</h3>
                <div className="space-y-8">
                  <ModelSettingsPanel
                    panelId="core"
                    panelName="Core Model"
                    settings={modelSettings.core}
                    allModels={allModels}
                    updateModelParameter={updateModelParameter}
                  />
                  <ModelSettingsPanel
                    panelId="plan"
                    panelName="Plan Model"
                    settings={modelSettings.plan}
                    allModels={allModels}
                    updateModelParameter={updateModelParameter}
                  />
                  <ModelSettingsPanel
                    panelId="compass"
                    panelName="Compass Model"
                    settings={modelSettings.compass}
                    allModels={allModels}
                    updateModelParameter={updateModelParameter}
                  />
                  <ModelSettingsPanel
                    panelId="general"
                    panelName="General Chat Model"
                    settings={modelSettings.general}
                    allModels={allModels}
                    updateModelParameter={updateModelParameter}
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="status" className="mt-6">
              <ConfigurationStatus
                apiKeys={apiKeys}
                modelSettings={modelSettings}
                validation={configValidation}
                onRefreshValidation={() => {
                  const appState = useAppStore.getState();
                  setConfigValidation(validationService.validateConfiguration(appState));
                }}
              />
            </TabsContent>

            <TabsContent value="data" className="mt-6">
              <DataManagement
                onExport={handleExportData}
                onImport={handleImportData}
                onClearAll={clearAllConfiguration}
                onSave={saveConfigurationToStorage}
              />
            </TabsContent>
          </ScrollArea>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export default SettingsModal;
