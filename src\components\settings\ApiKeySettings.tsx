
import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle, Eye, EyeOff, Shield, AlertTriangle } from 'lucide-react';
import { AppState } from '@/stores/types';
import configService from '@/services/configService';
import validationService from '@/services/validationService';

interface ApiKeySettingsProps {
  apiKeys: AppState['apiKeys'];
  updateApiKey: (service: 'openai' | 'anthropic' | 'google' | 'openrouter', key: string) => void;
}

const ApiKeySettings: React.FC<ApiKeySettingsProps> = ({ apiKeys, updateApiKey }) => {
  const [showKeys, setShowKeys] = useState<Record<string, boolean>>({});
  const apiKeyConfig = configService.loadApiKeys();

  const toggleShowKey = (service: string) => {
    setShowKeys(prev => ({ ...prev, [service]: !prev[service] }));
  };

  const getValidationStatus = (service: keyof AppState['apiKeys'], key: string) => {
    if (!key || key.trim().length === 0) return null;
    return validationService.validateApiKey(service, key, 'user-input');
  };

  const renderApiKeyField = (
    service: keyof AppState['apiKeys'],
    label: string,
    placeholder: string
  ) => {
    const config = apiKeyConfig[service];
    const isFromEnv = config.source.source === 'environment';
    const validation = getValidationStatus(service, config.value);
    const hasKey = config.value && config.value.trim().length > 0;

    return (
      <div key={service} className="space-y-2">
        <div className="flex items-center justify-between">
          <Label htmlFor={`${service}-key`} className="text-sm font-medium text-gray-300">
            {label}
          </Label>
          <div className="flex items-center gap-2">
            {isFromEnv && (
              <Badge variant="outline" className="border-blue-500 text-blue-300 text-xs">
                <Shield className="w-3 h-3 mr-1" />
                ENV
              </Badge>
            )}
            {hasKey && validation && (
              <div className="flex items-center gap-1">
                {validation.isValid ? (
                  <CheckCircle className="w-4 h-4 text-green-500" />
                ) : (
                  <XCircle className="w-4 h-4 text-red-500" />
                )}
              </div>
            )}
          </div>
        </div>

        <div className="relative">
          <Input
            id={`${service}-key`}
            type={showKeys[service] ? 'text' : 'password'}
            placeholder={isFromEnv ? 'Using environment variable' : placeholder}
            value={apiKeys[service]}
            onChange={(e) => updateApiKey(service, e.target.value)}
            disabled={isFromEnv}
            className={`bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500 pr-10 ${
              isFromEnv ? 'opacity-60 cursor-not-allowed' : ''
            } ${
              hasKey && validation && !validation.isValid ? 'border-red-500' : ''
            }`}
          />
          {hasKey && !isFromEnv && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0 hover:bg-gray-700"
              onClick={() => toggleShowKey(service)}
            >
              {showKeys[service] ? (
                <EyeOff className="w-4 h-4" />
              ) : (
                <Eye className="w-4 h-4" />
              )}
            </Button>
          )}
        </div>

        {hasKey && validation && !validation.isValid && (
          <div className="text-xs text-red-400 flex items-start gap-2">
            <AlertTriangle className="w-3 h-3 mt-0.5 flex-shrink-0" />
            <div>
              {validation.errors.map((error, index) => (
                <p key={index}>{error}</p>
              ))}
            </div>
          </div>
        )}

        {isFromEnv && (
          <p className="text-xs text-blue-400">
            This API key is loaded from an environment variable and cannot be changed here.
          </p>
        )}
      </div>
    );
  };
  return (
    <div>
      <h3 className="text-lg font-semibold mb-4 text-blue-400">API Keys</h3>
      <div className="space-y-6">
        {renderApiKeyField('openai', 'OpenAI API Key', 'sk-...')}
        {renderApiKeyField('anthropic', 'Anthropic API Key', 'sk-ant-...')}
        {renderApiKeyField('google', 'Google API Key', 'AIza...')}
        {renderApiKeyField('openrouter', 'OpenRouter API Key', 'sk-or-...')}
      </div>

      <div className="mt-6 p-4 bg-gray-800 rounded-lg border border-gray-700">
        <h4 className="text-sm font-medium text-gray-300 mb-2">Environment Variables</h4>
        <p className="text-xs text-gray-400 mb-2">
          You can set API keys using environment variables for enhanced security:
        </p>
        <div className="text-xs text-gray-500 space-y-1 font-mono">
          <p>VITE_OPENAI_API_KEY=your_openai_key</p>
          <p>VITE_ANTHROPIC_API_KEY=your_anthropic_key</p>
          <p>VITE_GOOGLE_API_KEY=your_google_key</p>
          <p>VITE_OPENROUTER_API_KEY=your_openrouter_key</p>
        </div>
        <p className="text-xs text-gray-400 mt-2">
          Environment variables take precedence over stored keys and cannot be overwritten through the UI.
        </p>
      </div>
    </div>
  );
};

export default ApiKeySettings;
